"use client"

import { useState, useTransition, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  ChevronLeft,
  ChevronRight,
  Package,
  FileText,
  Truck,
  Plus,
  Minus,
} from "lucide-react";
import { OrderItemForReturn, ReturnItemReason, ReturnReason } from "@/types/returns";
import { formatDate } from "@/lib/order-utils";
import Image from "next/image";
import { createReturn } from "@/app/actions/returns";
import { toast } from "sonner";

// Return reason labels
const RETURN_REASON_LABELS = {
  wrongItem: "Produs greșit",
  defective: "Produs defect", 
  damaged: "Produs deteriorat",
  notAsDescribed: "Nu corespunde descrierii",
  noLongerWanted: "Nu mai doresc produsul",
  other: "Altul",
};

const RETURN_ITEM_REASON_LABELS = {
  wrongItem: "Produs greșit",
  defective: "Produs defect",
  damaged: "Produs deteriorat", 
  notAsDescribed: "Nu corespunde descrierii",
  noLongerWanted: "Nu mai doresc produsul",
  other: "Altul",
};

interface NewReturnDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderItems: OrderItemForReturn[];
  onReturnCreated?: () => void;
}

interface SelectedItem {
  orderItemId: string;
  quantity: number;
  reason: ReturnItemReason;
  description?: string;
}

export default function NewReturnDialog({
  open,
  onOpenChange,
  orderItems,
  onReturnCreated,
}: NewReturnDialogProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(false);

  // Form data
  const [selectedItems, setSelectedItems] = useState<SelectedItem[]>([]);
  const [returnReason, setReturnReason] = useState<ReturnReason>("defective");
  const [additionalNotes, setAdditionalNotes] = useState("");
  const [shippingAddress, setShippingAddress] = useState({
    fullName: "",
    address: "",
    city: "",
    county: "",
    phoneNumber: "",
    notes: "",
  });

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setCurrentStep(1);
      setSelectedItems([]);
      setReturnReason("defective");
      setAdditionalNotes("");
      setShippingAddress({
        fullName: "",
        address: "",
        city: "",
        county: "",
        phoneNumber: "",
        notes: "",
      });
    }
  }, [open]);

  const steps = [
    { number: 1, title: "Select Item", icon: Package },
    { number: 2, title: "Return Details", icon: FileText },
    { number: 3, title: "Shipping", icon: Truck },
  ];

  const handleItemSelection = (orderItemId: string, checked: boolean) => {
    if (checked) {
      const orderItem = orderItems.find(item => item.id === orderItemId);
      if (orderItem) {
        setSelectedItems(prev => [...prev, {
          orderItemId,
          quantity: 1,
          reason: "defective",
          description: "",
        }]);
      }
    } else {
      setSelectedItems(prev => prev.filter(item => item.orderItemId !== orderItemId));
    }
  };

  const updateSelectedItem = (orderItemId: string, updates: Partial<SelectedItem>) => {
    setSelectedItems(prev => prev.map(item => 
      item.orderItemId === orderItemId ? { ...item, ...updates } : item
    ));
  };

  const handleQuantityChange = (orderItemId: string, delta: number) => {
    const orderItem = orderItems.find(item => item.id === orderItemId);
    if (!orderItem) return;

    setSelectedItems(prev => prev.map(item => {
      if (item.orderItemId === orderItemId) {
        const newQuantity = Math.max(1, Math.min(orderItem.availableQuantity, item.quantity + delta));
        return { ...item, quantity: newQuantity };
      }
      return item;
    }));
  };

  const canProceedToStep2 = selectedItems.length > 0;
  const canProceedToStep3 = returnReason && selectedItems.every(item => item.reason);
  const canSubmit = shippingAddress.fullName && shippingAddress.address && 
                   shippingAddress.city && shippingAddress.county && shippingAddress.phoneNumber;

  const handleSubmit = async () => {
    if (!canSubmit || selectedItems.length === 0) {
      toast.error("Vă rugăm să completați toate câmpurile obligatorii");
      return;
    }

    setIsLoading(true);
    startTransition(async () => {
      try {
        // Group selected items by order ID
        const itemsByOrder = selectedItems.reduce((acc, item) => {
          const orderItem = orderItems.find(oi => oi.id === item.orderItemId);
          if (orderItem) {
            const orderId = orderItem.order.id;
            if (!acc[orderId]) {
              acc[orderId] = [];
            }
            acc[orderId].push(item);
          }
          return acc;
        }, {} as Record<string, typeof selectedItems>);

        // Create returns for each order
        const results = await Promise.all(
          Object.entries(itemsByOrder).map(([orderId, items]) =>
            createReturn({
              orderId,
              reason: returnReason,
              additionalNotes: additionalNotes || undefined,
              items,
              shippingAddress,
            })
          )
        );

        // Check if all returns were successful
        const failedReturns = results.filter(result => !result.success);
        const successfulReturns = results.filter(result => result.success);

        if (failedReturns.length === 0) {
          if (successfulReturns.length === 1) {
            toast.success(`Returnarea ${successfulReturns[0].returnNumber} a fost creată cu succes`);
          } else {
            toast.success(`${successfulReturns.length} returnări au fost create cu succes`);
          }
          onReturnCreated?.();
          onOpenChange(false);
        } else if (successfulReturns.length > 0) {
          toast.warning(`${successfulReturns.length} returnări create cu succes, ${failedReturns.length} au eșuat`);
          onReturnCreated?.();
          onOpenChange(false);
        } else {
          toast.error(failedReturns[0].error || "A apărut o eroare la crearea returnărilor");
        }
      } catch (error) {
        console.error("Error creating returns:", error);
        toast.error("A apărut o eroare neașteptată la crearea returnărilor");
      } finally {
        setIsLoading(false);
      }
    });
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Select Item to Return</h3>
              <p className="text-sm text-muted-foreground">
                Selectează produsele pe care dorești să le returnezi
              </p>
            </div>

            {orderItems.length === 0 ? (
              <div className="text-center py-8 space-y-4">
                <Package className="h-12 w-12 text-muted-foreground mx-auto" />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Nu sunt produse disponibile</h3>
                  <p className="text-muted-foreground">
                    Nu aveți comenzi eligibile pentru returnare. Doar comenzile livrate în ultimele 14 zile pot fi returnate.
                  </p>
                </div>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto space-y-3">
                {orderItems.map((orderItem) => {
                const isSelected = selectedItems.some(item => item.orderItemId === orderItem.id);
                const selectedItem = selectedItems.find(item => item.orderItemId === orderItem.id);

                return (
                  <div key={orderItem.id} className="border rounded-lg p-4">
                    <div className="flex items-start gap-4">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleItemSelection(orderItem.id, !!checked)}
                      />
                      
                      <div className="relative h-16 w-16 flex-shrink-0">
                        <Image
                          src={orderItem.product.ImageUrl[0] || "/placeholder-product.jpg"}
                          alt={orderItem.product.Description_Local}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>

                      <div className="flex-1 space-y-2">
                        <div>
                          <h4 className="font-medium text-sm">
                            {orderItem.product.Description_Local}
                          </h4>
                          <h5 className="font-medium text-sm">
                            {orderItem.product.Material_Number}
                          </h5>
                          <p className="text-xs text-muted-foreground">
                            Order #{orderItem.order.orderNumber}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                            <span>Quantity: {orderItem.quantity}</span>
                            <span>Ordered {formatDate(orderItem.order.placedAt)}</span>
                          </div>
                        </div>

                        {isSelected && selectedItem && (
                          <div className="flex items-center gap-2 pt-2 border-t">
                            <span className="text-sm">Cantitate returnată:</span>
                            <div className="flex items-center gap-1">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuantityChange(orderItem.id, -1)}
                                disabled={selectedItem.quantity <= 1}
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <span className="w-8 text-center text-sm">{selectedItem.quantity}</span>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleQuantityChange(orderItem.id, 1)}
                                disabled={selectedItem.quantity >= orderItem.availableQuantity}
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            <span className="text-xs text-muted-foreground">
                              (max {orderItem.availableQuantity})
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
              </div>
            )}
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Return Details</h3>
              <p className="text-sm text-muted-foreground">
                Specifică motivul returnării și detalii adiționale
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-3">
                <Label className="text-sm font-medium">Return Reason</Label>
                <RadioGroup value={returnReason} onValueChange={(value) => setReturnReason(value as ReturnReason)}>
                  {Object.entries(RETURN_REASON_LABELS).map(([value, label]) => (
                    <div key={value} className="flex items-center space-x-2">
                      <RadioGroupItem value={value} id={value} />
                      <Label htmlFor={value} className="text-sm">{label}</Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              <div className="space-y-3">
                <Label className="text-sm font-medium">Additional Details</Label>
                <Textarea
                  placeholder="Please provide any additional information about your return..."
                  value={additionalNotes}
                  onChange={(e) => setAdditionalNotes(e.target.value)}
                  rows={4}
                />
              </div>

              {/* Item-specific reasons */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">Motiv pentru fiecare produs</Label>
                {selectedItems.map((selectedItem) => {
                  const orderItem = orderItems.find(item => item.id === selectedItem.orderItemId);
                  if (!orderItem) return null;

                  return (
                    <div key={selectedItem.orderItemId} className="border rounded-lg p-3 space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="relative h-12 w-12 flex-shrink-0">
                          <Image
                            src={orderItem.product.ImageUrl[0] || "/placeholder-product.jpg"}
                            alt={orderItem.product.Description_Local}
                            fill
                            className="object-cover rounded-md"
                          />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{orderItem.product.Description_Local}</h4>
                          <p className="text-xs text-muted-foreground">Cantitate: {selectedItem.quantity}</p>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Select
                          value={selectedItem.reason}
                          onValueChange={(value) => updateSelectedItem(selectedItem.orderItemId, { reason: value as ReturnItemReason })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Selectează motivul" />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.entries(RETURN_ITEM_REASON_LABELS).map(([value, label]) => (
                              <SelectItem key={value} value={value}>{label}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>

                        <Textarea
                          placeholder="Detalii adiționale pentru acest produs..."
                          value={selectedItem.description || ""}
                          onChange={(e) => updateSelectedItem(selectedItem.orderItemId, { description: e.target.value })}
                          rows={2}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Shipping Address</h3>
              <p className="text-sm text-muted-foreground">
                Adresa unde să trimitem eticheta de returnare
              </p>
            </div>

            <div className="space-y-4">
              <div className="space-y-3">
                <Label>Select Shipping Address</Label>
                <Select defaultValue="">
                  <SelectTrigger>
                    <SelectValue placeholder="-- Select a saved address --" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">-- Select a saved address --</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="firstName">First Name</Label>
                  <Input
                    id="firstName"
                    value={shippingAddress.fullName.split(' ')[0] || ''}
                    onChange={(e) => {
                      const lastName = shippingAddress.fullName.split(' ').slice(1).join(' ');
                      setShippingAddress(prev => ({ ...prev, fullName: `${e.target.value} ${lastName}`.trim() }));
                    }}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Last Name</Label>
                  <Input
                    id="lastName"
                    value={shippingAddress.fullName.split(' ').slice(1).join(' ') || ''}
                    onChange={(e) => {
                      const firstName = shippingAddress.fullName.split(' ')[0] || '';
                      setShippingAddress(prev => ({ ...prev, fullName: `${firstName} ${e.target.value}`.trim() }));
                    }}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Street Address</Label>
                <Input
                  id="address"
                  value={shippingAddress.address}
                  onChange={(e) => setShippingAddress(prev => ({ ...prev, address: e.target.value }))}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={shippingAddress.city}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, city: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="county">State</Label>
                  <Input
                    id="county"
                    value={shippingAddress.county}
                    onChange={(e) => setShippingAddress(prev => ({ ...prev, county: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="zipCode">ZIP Code</Label>
                <Input id="zipCode" />
              </div>

              <Separator />

              <div className="space-y-4">
                <h4 className="font-medium">Contact Information</h4>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input id="email" type="email" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={shippingAddress.phoneNumber}
                      onChange={(e) => setShippingAddress(prev => ({ ...prev, phoneNumber: e.target.value }))}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">Start New Return</DialogTitle>
              <DialogDescription>
                Urmează pașii pentru a crea o nouă returnare
              </DialogDescription>
            </div>
            {/* <Button variant="ghost" size="sm" onClick={() => onOpenChange(false)}>
              <X className="h-4 w-4" />
            </Button> */}
          </div>
        </DialogHeader>

        {/* Step indicator */}
        <div className="flex items-center justify-center space-x-8 py-4">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <div className={`flex items-center space-x-2 ${
                currentStep === step.number ? 'text-primary' : 
                currentStep > step.number ? 'text-green-600' : 'text-muted-foreground'
              }`}>
                <div className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                  currentStep === step.number ? 'border-primary bg-primary text-primary-foreground' :
                  currentStep > step.number ? 'border-green-600 bg-green-600 text-white' :
                  'border-muted-foreground'
                }`}>
                  <step.icon className="h-4 w-4" />
                </div>
                <span className="text-sm font-medium">{step.title}</span>
              </div>
              {index < steps.length - 1 && (
                <div className={`mx-4 h-px w-12 ${
                  currentStep > step.number ? 'bg-green-600' : 'bg-muted-foreground'
                }`} />
              )}
            </div>
          ))}
        </div>

        <Separator />

        {/* Step content */}
        <div className="py-6">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(prev => Math.max(1, prev - 1))}
            disabled={currentStep === 1}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>

          {currentStep < 3 ? (
            <Button
              onClick={() => setCurrentStep(prev => prev + 1)}
              disabled={
                (currentStep === 1 && (!canProceedToStep2 || orderItems.length === 0)) ||
                (currentStep === 2 && !canProceedToStep3)
              }
              className="flex items-center gap-2"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isLoading || isPending}
              className="flex items-center gap-2"
            >
              {isLoading ? "Se trimite..." : "Submit Return Request"}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
