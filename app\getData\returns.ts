"server-only"

import { Prisma } from "@/generated/prisma"
import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"

import { toSafeNumber } from "@/lib/utils"
import { cuidSchema, returnFiltersSchema } from "@/lib/zod"
import {
  Return,
  ReturnsResponse,
  ReturnFilters,
  RawReturnFromPrisma,
  OrderItemForReturn,
  ReturnStatistics,
  ReturnStatus,
} from "@/types/returns"
import { cache } from "react"
//import type { ReturnStatus as PrismaReturnStatus } from "@/generated/prisma";

/**
 * Transform raw return data from Prisma to UI-friendly format
 */
function transformReturnData(rawReturn: RawReturnFromPrisma): Return {
  return {
    id: rawReturn.id,
    returnNumber: rawReturn.returnNumber,
    status: rawReturn.status,
    reason: rawReturn.reason,
    additionalNotes: rawReturn.additionalNotes || undefined,
    isApproved: rawReturn.isApproved || undefined,
    approvedBy: rawReturn.approvedBy || undefined,
    approvedAt: rawReturn.approvedAt?.toISOString(),
    rejectionReason: rawReturn.rejectionReason || undefined,
    refundAmount: toSafeNumber(rawReturn.refundAmount) || undefined,
    refundMethod: rawReturn.refundMethod || undefined,
    refundedAt: rawReturn.refundedAt?.toISOString(),
    refundReference: rawReturn.refundReference || undefined,
    returnShippingLabel: rawReturn.returnShippingLabel || undefined,
    receivedAt: rawReturn.receivedAt?.toISOString(),
    inspectedAt: rawReturn.inspectedAt?.toISOString(),
    createdAt: rawReturn.createdAt.toISOString(),
    updatedAt: rawReturn.updatedAt.toISOString(),
    order: {
      id: rawReturn.order.id,
      orderNumber: rawReturn.order.orderNumber,
      placedAt: rawReturn.order.placedAt.toISOString(),
    },
    returnItems: rawReturn.returnItems.map(item => ({
      id: item.id,
      quantity: item.quantity,
      reason: item.reason,
      condition: item.condition,
      description: item.description || undefined,
      isReceived: item.isReceived,
      isInspected: item.isInspected,
      inspectionNotes: item.inspectionNotes || undefined,
      inspectionResult: item.inspectionResult || undefined,
      orderItem: {
        id: item.orderItem.id,
        quantity: item.orderItem.quantity,
        price: toSafeNumber(item.orderItem.price) || 0,
        product: {
          id: item.orderItem.product.id,
          Material_Number: item.orderItem.product.Material_Number,
          Description_Local: item.orderItem.product.Description_Local || item.orderItem.product.Material_Number,
          ImageUrl: item.orderItem.product.ImageUrl,
        },
      },
    })),
  };
}

/**
 * Get user returns with filtering and pagination
 */
export const getUserReturns = cache(async (
  userId: string,
  filters: ReturnFilters
): Promise<ReturnsResponse> => {
  try {
    // Validate inputs
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getUserReturns] Invalid userId: ${userId}`);
      throw new Error("Invalid user ID");
    }

    const filtersValidation = returnFiltersSchema.safeParse(filters);
    if (!filtersValidation.success) {
      logger.error(`[getUserReturns] Invalid filters:`, filtersValidation.error);
      throw new Error("Invalid filters");
    }

    const validatedFilters = filtersValidation.data;
    const { status, search, page, limit, dateFrom, dateTo } = validatedFilters;

    // Build where clause
    const where: Prisma.ReturnWhereInput = {
      order: {
        userId: userId,
      },
    };

    // Add status filter
    if (status && status !== 'all') {
      where.status = status as ReturnStatus;
    }

    // Add search filter
    if (search) {
      where.OR = [
        { returnNumber: { contains: search, mode: 'insensitive' } },
        { order: { orderNumber: { contains: search, mode: 'insensitive' } } },
        { additionalNotes: { contains: search, mode: 'insensitive' } },
      ];
    }

    // Add date filters
    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // Get total count for pagination
    const total = await withRetry(() =>
      prisma.return.count({ where })
    );

    const totalPages = Math.ceil(total / limit);
    const skip = (page - 1) * limit;

    // Fetch returns with related data
    const rawReturns = await withRetry(() =>
      prisma.return.findMany({
        where,
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              placedAt: true,
            },
          },
          returnItems: {
            include: {
              orderItem: {
                include: {
                  product: {
                    select: {
                      id: true,
                      Material_Number: true,
                      Description_Local: true,
                      ImageUrl: true,
                    },
                  },
                },
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      })
    ) as RawReturnFromPrisma[];

    // Transform data
    const returns = rawReturns.map(transformReturnData);

    return {
      returns,
      pagination: {
        total,
        pages: totalPages,
        currentPage: page,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };

  } catch (error) {
    logger.error(`[getUserReturns] Error fetching returns for user ${userId}:`, error);
    throw new Error("Failed to fetch returns");
  }
});

/**
 * Get a specific return by ID
 */
export const getReturnById = cache(async (
  returnId: string,
  userId: string
): Promise<Return | null> => {
  try {
    // Validate inputs
    const returnIdValidation = cuidSchema.safeParse(returnId);
    const userIdValidation = cuidSchema.safeParse(userId);

    if (!returnIdValidation.success || !userIdValidation.success) {
      logger.error(`[getReturnById] Invalid IDs: returnId=${returnId}, userId=${userId}`);
      return null;
    }

    const rawReturn = await withRetry(() =>
      prisma.return.findFirst({
        where: {
          id: returnId,
          order: {
            userId: userId,
          },
        },
        include: {
          order: {
            select: {
              id: true,
              orderNumber: true,
              placedAt: true,
            },
          },
          returnItems: {
            include: {
              orderItem: {
                include: {
                  product: {
                    select: {
                      id: true,
                      Material_Number: true,
                      Description_Local: true,
                      ImageUrl: true,
                    },
                  },
                },
              },
            },
          },
        },
      })
    ) as RawReturnFromPrisma | null;

    if (!rawReturn) {
      return null;
    }

    return transformReturnData(rawReturn);

  } catch (error) {
    logger.error(`[getReturnById] Error fetching return ${returnId}:`, error);
    return null;
  }
});

/**
 * Get order items available for return (within 14 days for EU)
 */
export const getOrderItemsForReturn = cache(async (
  orderId: string,
  userId: string
): Promise<OrderItemForReturn[]> => {
  try {
    // Validate inputs
    const orderIdValidation = cuidSchema.safeParse(orderId);
    const userIdValidation = cuidSchema.safeParse(userId);

    if (!orderIdValidation.success || !userIdValidation.success) {
      logger.error(`[getOrderItemsForReturn] Invalid IDs: orderId=${orderId}, userId=${userId}`);
      return [];
    }

    // Calculate 14 days ago for EU return policy
    const fourteenDaysAgo = new Date();
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

    // Get order with items and existing returns (only orders within 14 days)
    const order = await withRetry(() =>
      prisma.order.findFirst({
        where: {
          id: orderId,
          userId: userId,
          placedAt: {
            gte: fourteenDaysAgo, // Only orders placed within last 14 days
          },
        },
        include: {
          orderItems: {
            include: {
              product: {
                select: {
                  id: true,
                  Material_Number: true,
                  Description_Local: true,
                  ImageUrl: true,
                },
              },
              ReturnItem: {
                select: {
                  quantity: true,
                },
              },
            },
          },
        },
      })
    );

    if (!order) {
      return [];
    }

    // Transform to OrderItemForReturn format
    return order.orderItems.map(item => {
      const returnedQuantity = item.ReturnItem.reduce((sum, returnItem) => sum + returnItem.quantity, 0);
      const availableQuantity = item.quantity - returnedQuantity;

      return {
        id: item.id,
        quantity: item.quantity,
        price: toSafeNumber(item.price) || 0,
        product: {
          id: item.product.id,
          Material_Number: item.product.Material_Number,
          Description_Local: item.product.Description_Local || item.product.Material_Number,
          ImageUrl: item.product.ImageUrl,
        },
        order: {
          id: order.id,
          orderNumber: order.orderNumber,
          placedAt: order.placedAt.toISOString(),
        },
        availableQuantity: Math.max(0, availableQuantity),
      };
    }).filter(item => item.availableQuantity > 0); // Only return items that can still be returned

  } catch (error) {
    logger.error(`[getOrderItemsForReturn] Error fetching order items for return:`, error);
    return [];
  }
});

/**
 * Get all returnable order items for a user (within 14 days for EU)
 */
export const getReturnableOrderItems = cache(async (
  userId: string
): Promise<OrderItemForReturn[]> => {
  try {
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getReturnableOrderItems] Invalid userId: ${userId}`);
      return [];
    }

    // Calculate 14 days ago for EU return policy
    const fourteenDaysAgo = new Date();
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);

    // Get orders with items and existing returns (only orders within 14 days)
    const orders = await withRetry(() =>
      prisma.order.findMany({
        where: {
          userId: userId,
          placedAt: {
            gte: fourteenDaysAgo, // Only orders placed within last 14 days
          },
          // orderStatus: {
          //   in: ['completa', 'livrata'], // Only completed/delivered orders can be returned
          // },
        },
        select: {
          id: true,
          orderNumber: true,
          placedAt: true,
          orderItems: {
            select: {
              id: true,
              quantity: true,
              price: true,
              product: {
                select: {
                  id: true,
                  Material_Number: true,
                  Description_Local: true,
                  ImageUrl: true,
                },
              },
              ReturnItem: {
                select: {
                  quantity: true,
                },
              },
            },
          },
        },
        orderBy: {
          placedAt: 'desc',
        },
      })
    );

    if (!orders.length) {
      return [];
    }

    // Transform to OrderItemForReturn format and filter out fully returned items
    const returnableItems: OrderItemForReturn[] = [];

    orders.forEach(order => {
      order.orderItems.forEach(item => {
        const returnedQuantity = item.ReturnItem.reduce((sum, returnItem) => sum + returnItem.quantity, 0);
        const availableQuantity = item.quantity - returnedQuantity;

        if (availableQuantity > 0) {
          returnableItems.push({
            id: item.id,
            quantity: item.quantity,
            price: toSafeNumber(item.price) || 0,
            product: {
              id: item.product.id,
              Material_Number: item.product.Material_Number,
              Description_Local: item.product.Description_Local || item.product.Material_Number,
              ImageUrl: item.product.ImageUrl,
            },
            order: {
              id: order.id,
              orderNumber: order.orderNumber,
              placedAt: order.placedAt.toISOString(),
            },
            availableQuantity: Math.max(0, availableQuantity),
          });
        }
      });
    });

    return returnableItems;

  } catch (error) {
    logger.error(`[getReturnableOrderItems] Error fetching returnable order items:`, error);
    return [];
  }
});

/**
 * Get return statistics for a user
 */
export const getReturnStatistics = cache(async (userId: string): Promise<ReturnStatistics> => {
  try {
    const userIdValidation = cuidSchema.safeParse(userId);
    if (!userIdValidation.success) {
      logger.error(`[getReturnStatistics] Invalid userId: ${userId}`);
      return { total: 0, pending: 0, approved: 0, completed: 0, rejected: 0 };
    }

    const stats = await withRetry(() =>
      prisma.return.groupBy({
        by: ['status'],
        where: {
          order: {
            userId: userId,
          },
        },
        _count: {
          id: true,
        },
      })
    );

    const result: ReturnStatistics = {
      total: 0,
      pending: 0,
      approved: 0,
      completed: 0,
      rejected: 0,
    };

    stats.forEach(stat => {
      result.total += stat._count.id;
      
      switch (stat.status) {
        case 'requested':
        case 'awaitingReceipt':
          result.pending += stat._count.id;
          break;
        case 'approved':
        case 'received':
        case 'inspected':
        case 'refundIssued':
          result.approved += stat._count.id;
          break;
        case 'completed':
          result.completed += stat._count.id;
          break;
        case 'rejected':
        case 'cancelled':
          result.rejected += stat._count.id;
          break;
      }
    });

    return result;

  } catch (error) {
    logger.error(`[getReturnStatistics] Error fetching return statistics:`, error);
    return { total: 0, pending: 0, approved: 0, completed: 0, rejected: 0 };
  }
});

/**
 * Generate return timeline events
 */
// export function generateReturnTimeline(returnData: Return): ReturnTimelineEvent[] {
//   const events: ReturnTimelineEvent[] = [];
//   const currentStatus = returnData.status;

//   // Define the standard return flow
//   const statusFlow: { status: ReturnStatus; title: string; description: string }[] = [
//     { status: 'requested', title: 'Returnare solicitată', description: 'Cererea de returnare a fost trimisă' },
//     { status: 'approved', title: 'Returnare aprobată', description: 'Cererea de returnare a fost aprobată' },
//     { status: 'awaitingReceipt', title: 'În așteptarea primirii', description: 'Așteptăm să primim produsele' },
//     { status: 'received', title: 'Produse primite', description: 'Produsele au fost primite în depozit' },
//     { status: 'inspected', title: 'Produse verificate', description: 'Produsele au fost verificate și evaluate' },
//     { status: 'refundIssued', title: 'Rambursare procesată', description: 'Rambursarea a fost procesată' },
//     { status: 'completed', title: 'Returnare finalizată', description: 'Procesul de returnare a fost finalizat' },
//   ];

//   // Handle rejected status separately
//   if (currentStatus === 'rejected') {
//     events.push({
//       date: returnData.createdAt,
//       status: 'requested',
//       title: 'Returnare solicitată',
//       description: 'Cererea de returnare a fost trimisă',
//       isCompleted: true,
//       isCurrent: false,
//     });

//     events.push({
//       date: returnData.approvedAt || returnData.updatedAt,
//       status: 'rejected',
//       title: 'Returnare respinsă',
//       description: returnData.rejectionReason || 'Cererea de returnare a fost respinsă',
//       isCompleted: true,
//       isCurrent: true,
//     });

//     return events;
//   }

//   // Handle cancelled status separately
//   if (currentStatus === 'cancelled') {
//     events.push({
//       date: returnData.createdAt,
//       status: 'requested',
//       title: 'Returnare solicitată',
//       description: 'Cererea de returnare a fost trimisă',
//       isCompleted: true,
//       isCurrent: false,
//     });

//     events.push({
//       date: returnData.updatedAt,
//       status: 'cancelled',
//       title: 'Returnare anulată',
//       description: 'Cererea de returnare a fost anulată',
//       isCompleted: true,
//       isCurrent: true,
//     });

//     return events;
//   }

//   // Generate events for normal flow
//   let currentStatusReached = false;

//   statusFlow.forEach(step => {
//     let eventDate = returnData.createdAt;
//     let isCompleted = false;
//     let isCurrent = false;

//     // Determine if this step is completed and get the appropriate date
//     switch (step.status) {
//       case 'requested':
//         eventDate = returnData.createdAt;
//         isCompleted = true;
//         break;
//       case 'approved':
//         if (returnData.approvedAt) {
//           eventDate = returnData.approvedAt;
//           isCompleted = true;
//         }
//         break;
//       case 'awaitingReceipt':
//         if (['awaitingReceipt', 'received', 'inspected', 'refundIssued', 'completed'].includes(currentStatus)) {
//           eventDate = returnData.approvedAt || returnData.updatedAt;
//           isCompleted = true;
//         }
//         break;
//       case 'received':
//         if (returnData.receivedAt) {
//           eventDate = returnData.receivedAt;
//           isCompleted = true;
//         }
//         break;
//       case 'inspected':
//         if (returnData.inspectedAt) {
//           eventDate = returnData.inspectedAt;
//           isCompleted = true;
//         }
//         break;
//       case 'refundIssued':
//         if (returnData.refundedAt) {
//           eventDate = returnData.refundedAt;
//           isCompleted = true;
//         }
//         break;
//       case 'completed':
//         if (currentStatus === 'completed') {
//           eventDate = returnData.updatedAt;
//           isCompleted = true;
//         }
//         break;
//     }

//     // Check if this is the current step
//     if (step.status === currentStatus) {
//       isCurrent = true;
//       currentStatusReached = true;
//     }

//     // Only add future steps if they haven't been reached yet
//     if (!currentStatusReached || isCompleted || isCurrent) {
//       events.push({
//         date: eventDate,
//         status: step.status,
//         title: step.title,
//         description: step.description,
//         isCompleted,
//         isCurrent,
//       });
//     }
//   });

//   return events;
// }
